package postgres

import (
	"github.com/SneatX/phillips_go/internal/core/domain"
	appErrors "github.com/SneatX/phillips_go/internal/shared/errors"
	"gorm.io/gorm"
)

type categoryRepository struct {
	DB *gorm.DB
}

func NewCategoryRepository(DB *gorm.DB) *categoryRepository {
	return &categoryRepository{DB: DB}
}

func (r *categoryRepository) GetAll() ([]*domain.Category, error) {
	var categories []*domain.Category
	if err := r.DB.Find(&categories).Error; err != nil {
		return nil, appErrors.NewInternalError("failed to retrieve all categories", err)
	}
	return categories, nil
}

func (r *categoryRepository) Create(category *domain.Category) (*domain.Category, error) {
	if result := r.DB.Create(&category); result.Error != nil {
		return nil, appErrors.NewInternalError("failed to create category", result.Error)
	}
	return category, nil
}

func (r *categoryRepository) CreateAssignmentCategory(assignmentCategory *domain.AssignmentCategory) (*domain.AssignmentCategory, error) {
	if result := r.DB.Create(&assignmentCategory); result.Error != nil {
		return nil, appErrors.NewInternalError("failed to create assignment category", result.Error)
	}
	return assignmentCategory, nil
}
