package routes

import (
	"github.com/SneatX/phillips_go/internal/adapters/http/handlers"
	"github.com/SneatX/phillips_go/internal/adapters/http/middleware"
	jwt "github.com/SneatX/phillips_go/internal/platform/token"
	"github.com/gin-gonic/gin"
)

func RegisterCategoryRoutes(rg *gin.RouterGroup, handler *handlers.CategoryHandler, jwtManager *jwt.JWTManager) {
	publicCategories := rg.Group("/categories")

	protectedCategories := publicCategories.Group("", middleware.JWTAuthMiddleware(jwtManager))
	{
		protectedCategories.GET("", handler.GetAllCategories)

		protectedCategories.POST("", handler.CreateCategory)
		protectedCategories.POST("/assignment/:id", handler.CreateAssignmentCategory)
	}
}
